import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D # 导入3D绘图工具
from scipy.optimize import least_squares
from matplotlib.patches import Ellipse
import sys
import io
import os
import argparse
import time
from tqdm import tqdm

# 解决控制台输出编码问题
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 尝试导入TensorFlow，如果失败则设置标志
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import Dense, Input, Concatenate, BatchNormalization, Dropout, GaussianNoise
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
    USE_TENSORFLOW = True
    print("成功加载TensorFlow。")
except ImportError:
    USE_TENSORFLOW = False
    print("未能加载TensorFlow，将只使用传统方法。")

# ==============================================================================
# 1. 平台运动与几何关系 (Platform Motion and Geometry)
# ==============================================================================

def weave(g, T, del_T, alt_kft, vel):
    """平台导航数据生成函数"""
    dt = del_T
    dtt = del_T/20
    accelh = g * 9.81
    accelv = 0.5 * 9.81
    alt0 = alt_kft * 1000 * 0.3048

    dtr = np.pi / 180
    maxturn = 30 * dtr
    vhoriz = vel
    radturn = (vhoriz**2) / accelh
    turnrate = vel / radturn
    turndur = 2 * maxturn / turnrate
    turndur = np.floor(turndur/dtt) * dtt
    gamma0 = -np.arcsin(min(accelv*turndur/vel, 0.5))/2

    px, py, pz = 0, 0, alt0
    XYZA, XYZADOT, ah = [], [], []
    itt = 0

    for tt in np.arange(0, T + dtt, dtt):
        trem = tt % (2 * turndur)
        if trem < turndur:
            turnangl = maxturn - turnrate * trem
            ahoriz = accelh
            az = accelv
        else:
            turnangl = -maxturn + turnrate * (trem - turndur)
            ahoriz = -accelh
            az = -accelv

        vx = vhoriz * np.cos(turnangl)
        vy = -vhoriz * np.sin(turnangl)

        if itt > 0:
            vz = XYZADOT[-1][2] + az * dtt if XYZADOT else vel * np.sin(gamma0)
            px += vx * dtt
            py += vy * dtt
            pz += vz * dtt
        else:
            vz = vel * np.sin(gamma0)

        if abs(np.mod(itt * dtt, dt)) < 1e-6:
            XYZA.append([px, py, pz])
            XYZADOT.append([vx, vy, vz])
            ah.append(ahoriz)

        itt += 1

    XYZA = np.array(XYZA).T
    XYZADOT = np.array(XYZADOT).T
    ah = np.array(ah)

    hdg = np.pi/2 - np.arctan2(XYZADOT[1, :], XYZADOT[0, :])
    long_vect = np.vstack([np.sin(hdg), np.cos(hdg), np.zeros_like(hdg)])
    vect_norms = np.sqrt(np.sum(long_vect**2, axis=0))
    long_vect = long_vect / vect_norms

    return XYZA[0, :], XYZA[1, :], XYZA[2, :], XYZADOT[0, :], XYZADOT[1, :], XYZADOT[2, :], long_vect


def calculate_measurements(params, Plat_Nav_Data, mu_vect, fo):
    """(保持不变) 计算理想的多普勒和AOA测量值"""
    c = 2.998e8
    lambda_ = c / fo
    xe, ye, ze, fo_est = params
    Px, Py, Pz, Vx, Vy, Vz = Plat_Nav_Data[0, :], Plat_Nav_Data[1, :], Plat_Nav_Data[2, :], Plat_Nav_Data[3, :], Plat_Nav_Data[4, :], Plat_Nav_Data[5, :]
    R = np.sqrt((Px - xe)**2 + (Py - ye)**2 + (Pz - ze)**2)
    f = fo_est - (fo_est/c)*(Vx*(Px-xe) + Vy*(Py-ye) + Vz*(Pz-ze))/R
    phi = - (2*np.pi/lambda_)*(mu_vect[0,:]*(Px-xe) + mu_vect[1,:]*(Py-ye) + mu_vect[2,:]*(Pz-ze))/R
    return f, phi

# ==============================================================================
# 2. IQ数据生成与信号处理 (IQ Data Generation and Signal Processing)
# ==============================================================================

def generate_iq_data(p_true, platform_pos, platform_vel, mu_vect_single, fo, snr_db, L=2, d=0.5, fs=2e4, num_samples=2048):
    """改进的IQ数据生成函数 - 包含更真实的信号模型"""
    c = 2.998e8
    lambda_ = c / fo
    xe, ye, ze, fo_true = p_true
    Px, Py, Pz = platform_pos
    Vx, Vy, Vz = platform_vel

    R_vec = np.array([Px - xe, Py - ye, Pz - ze])
    R = np.linalg.norm(R_vec)
    los_vec_norm = R_vec / R

    doppler_freq = -(fo / c) * np.dot(platform_vel, los_vec_norm)
    cos_theta = -np.dot(los_vec_norm, mu_vect_single)

    t = np.arange(num_samples) / fs
    signal_power = 1.0
    iq_data = np.zeros((L, num_samples), dtype=np.complex128)

    # 添加频率漂移和相位噪声
    freq_drift = np.random.normal(0, 0.1) * doppler_freq  # 小的频率漂移
    phase_noise_std = 0.01  # 相位噪声标准差

    for i in range(L):
        # 基本相位偏移
        phase_shift = (2 * np.pi * d * i / lambda_) * cos_theta

        # 添加相位噪声
        phase_noise = np.random.normal(0, phase_noise_std, num_samples)
        phase_noise = np.cumsum(phase_noise)  # 积分相位噪声

        # 生成信号
        instantaneous_phase = 2 * np.pi * (doppler_freq + freq_drift) * t + phase_shift + phase_noise
        signal = np.exp(1j * instantaneous_phase)

        # 添加幅度起伏
        amplitude_variation = 1.0 + 0.05 * np.random.normal(0, 1, num_samples)
        signal = signal * amplitude_variation

        iq_data[i, :] = signal

    # 添加噪声
    snr_linear = 10**(snr_db / 10.0)
    noise_power = signal_power / snr_linear
    noise_std = np.sqrt(noise_power / 2)

    # 添加相关噪声（模拟接收机噪声）
    noise_correlation = 0.1  # 噪声相关性
    independent_noise = (np.random.normal(0, noise_std, size=iq_data.shape) +
                        1j * np.random.normal(0, noise_std, size=iq_data.shape))

    if L > 1:
        # 添加通道间相关噪声
        common_noise = (np.random.normal(0, noise_std * noise_correlation, size=(1, num_samples)) +
                       1j * np.random.normal(0, noise_std * noise_correlation, size=(1, num_samples)))
        common_noise = np.repeat(common_noise, L, axis=0)
        noise = independent_noise * (1 - noise_correlation) + common_noise
    else:
        noise = independent_noise

    return iq_data + noise

def extract_aoa_doppler_from_iq(iq_data, fo, fs, L=2, d=0.5):
    """基于物理原理的IQ数据特征提取方法"""
    num_samples = iq_data.shape[1]
    c = 2.998e8
    lambda_ = c / fo

    # --- 改进的多普勒估计 ---
    # 使用所有通道的平均信号
    avg_signal = np.mean(iq_data, axis=0)

    # 使用自相关方法估计频率
    autocorr = np.correlate(avg_signal, avg_signal, mode='full')
    autocorr = autocorr[len(autocorr)//2:]

    # FFT方法作为备选
    window = np.hanning(num_samples)
    windowed_signal = avg_signal * window
    fft_result = np.fft.fft(windowed_signal)
    fft_freqs = np.fft.fftfreq(num_samples, 1/fs)
    power_spectrum = np.abs(fft_result)**2
    peak_index = np.argmax(power_spectrum)
    doppler_est = fft_freqs[peak_index]
    f_est = fo + doppler_est

    # --- 基于物理原理的AOA特征提取 ---
    if L >= 2:
        # 1. 互相关分析
        cross_correlation = np.mean(iq_data[1, :] * np.conj(iq_data[0, :]))
        phase_diff = np.angle(cross_correlation)
        corr_magnitude = np.abs(cross_correlation)

        # 2. 归一化相关系数
        power_0 = np.mean(np.abs(iq_data[0, :])**2)
        power_1 = np.mean(np.abs(iq_data[1, :])**2)
        normalized_corr = corr_magnitude / np.sqrt(power_0 * power_1 + 1e-12)

        # 3. 基于相位差的AOA估计
        # 将相位差转换为角度信息
        phase_diff_unwrapped = np.unwrap([0, phase_diff])[1]

        # 计算cos(theta) - 这是关键的物理量
        cos_theta_raw = phase_diff_unwrapped * lambda_ / (2 * np.pi * d)
        cos_theta = np.clip(cos_theta_raw, -1, 1)  # 限制在物理范围内

        # 4. 信号质量评估
        # SNR估计
        signal_power = np.mean([power_0, power_1])
        noise_power_est = np.var(np.real(avg_signal)) + np.var(np.imag(avg_signal))
        snr_est = 10 * np.log10(signal_power / (noise_power_est + 1e-12))

        # 5. 通道一致性检查
        phase_0 = np.angle(iq_data[0, :])
        phase_1 = np.angle(iq_data[1, :])
        phase_stability = 1.0 - np.std(np.unwrap(phase_1 - phase_0)) / np.pi
        phase_stability = np.clip(phase_stability, 0, 1)

        # 6. 幅度特征
        amp_0 = np.abs(iq_data[0, :])
        amp_1 = np.abs(iq_data[1, :])
        amp_ratio = np.mean(amp_1) / (np.mean(amp_0) + 1e-12)
        amp_correlation = np.corrcoef(amp_0, amp_1)[0, 1]
        if np.isnan(amp_correlation):
            amp_correlation = 0.0

        features = [
            normalized_corr,      # 归一化相关系数
            phase_diff,          # 相位差
            cos_theta,           # cos(theta) - 关键AOA特征
            snr_est,             # SNR估计
            phase_stability,     # 相位稳定性
            amp_ratio,           # 幅度比
            amp_correlation,     # 幅度相关性
            doppler_est / 1000   # 归一化的多普勒频移
        ]
    else:
        features = [0, 0, 0, 0, 0, 0, 0, 0]

    return f_est, features


# ==============================================================================
# 3. 神经网络模型与训练 (Neural Network Model and Training)
# ==============================================================================

def build_neural_model(input_shape_measurements=9, input_shape_platform=6):
    """改进的神经网络模型 - 适应更丰富的特征"""
    measurement_input = Input(shape=(input_shape_measurements,), name='measurement_input')
    platform_input = Input(shape=(input_shape_platform,), name='platform_input')

    # 测量特征分支 - 专门处理IQ特征
    meas_branch = Dense(64, activation='relu')(measurement_input)
    meas_branch = BatchNormalization()(meas_branch)
    meas_branch = Dropout(0.2)(meas_branch)

    meas_branch = Dense(32, activation='relu')(meas_branch)
    meas_branch = BatchNormalization()(meas_branch)

    # 平台特征分支 - 专门处理平台数据
    plat_branch = Dense(32, activation='relu')(platform_input)
    plat_branch = BatchNormalization()(plat_branch)
    plat_branch = Dropout(0.2)(plat_branch)

    plat_branch = Dense(16, activation='relu')(plat_branch)
    plat_branch = BatchNormalization()(plat_branch)

    # 特征融合
    combined = Concatenate()([meas_branch, plat_branch])
    x = GaussianNoise(0.005)(combined)

    # 融合后的深层网络
    x = Dense(128, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dropout(0.3)(x)

    x = Dense(64, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dropout(0.2)(x)

    x = Dense(32, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dropout(0.1)(x)

    # 输出层 - 只预测位置(x,y,z)，不预测频率
    output = Dense(3, name='position_output')(x)

    model = Model(inputs=[measurement_input, platform_input], outputs=output)

    # 使用自定义损失函数，重点优化位置精度
    def position_loss(y_true, y_pred):
        # 位置误差
        position_error = y_true - y_pred

        # 使用Huber损失，对大误差不那么敏感
        huber_delta = 50.0  # 50米阈值
        abs_error = tf.abs(position_error)
        quadratic = tf.minimum(abs_error, huber_delta)
        linear = abs_error - quadratic
        huber_loss = 0.5 * quadratic**2 + huber_delta * linear

        return tf.reduce_mean(huber_loss)

    optimizer = Adam(learning_rate=0.001, clipnorm=1.0)
    model.compile(optimizer=optimizer, loss=position_loss, metrics=['mae'])

    return model


def generate_training_data(num_scenarios=1000, snr_range=(12, 25), area_size=5000):
    """(保持不变) 生成训练数据"""
    print(f"正在生成 {num_scenarios} 个场景的训练数据...")
    X_measurements, X_platform, Y_positions = [], [], []
    del_T = 0.1
    fo = 1e9
    
    for i in tqdm(range(num_scenarios), desc="生成训练数据"):
        g = np.random.uniform(1.5, 3.0)
        T = np.random.uniform(8, 15)
        alt_kft = np.random.uniform(8, 12)
        vel = np.random.uniform(180, 250)
        
        x_true = np.random.uniform(-area_size, area_size)
        y_true = np.random.uniform(-area_size, area_size)
        z_true = np.random.uniform(0, 500)
        fo_true = fo * np.random.uniform(0.999, 1.001)
        p_true = np.array([x_true, y_true, z_true, fo_true])
        
        snr_db = np.random.uniform(*snr_range)
        
        Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
        
        for j in range(len(Px)):
            platform_pos = np.array([Px[j], Py[j], Pz[j]])
            platform_vel = np.array([Vx[j], Vy[j], Vz[j]])
            mu_vect_single = mu_vect[:, j]
            
            iq_data = generate_iq_data(p_true, platform_pos, platform_vel, mu_vect_single, fo, snr_db)
            f_noisy, features = extract_aoa_doppler_from_iq(iq_data, fo, fs=2e4)

            # 组合频率和其他特征
            measurement = np.array([f_noisy] + features)
            platform = np.array([Px[j], Py[j], Pz[j], Vx[j], Vy[j], Vz[j]])

            X_measurements.append(measurement)
            X_platform.append(platform)
            Y_positions.append(p_true[:3])  # 只使用位置信息，不包括频率

    print("数据生成完成。")
    return np.array(X_measurements), np.array(X_platform), np.array(Y_positions)


def train_neural_model(X_measurements, X_platform, Y_positions, epochs=50, batch_size=128):
    """(保持不变) 训练神经网络模型"""
    
    measurement_mean = X_measurements.mean(axis=0)
    measurement_std = X_measurements.std(axis=0)
    X_measurements_norm = (X_measurements - measurement_mean) / (measurement_std + 1e-8)

    platform_mean = X_platform.mean(axis=0)
    platform_std = X_platform.std(axis=0)
    X_platform_norm = (X_platform - platform_mean) / (platform_std + 1e-8)

    position_mean = Y_positions.mean(axis=0)
    position_std = Y_positions.std(axis=0)
    Y_positions_norm = (Y_positions - position_mean) / (position_std + 1e-8)

    normalization_params = {
        'measurement_mean': measurement_mean, 'measurement_std': measurement_std,
        'platform_mean': platform_mean, 'platform_std': platform_std,
        'position_mean': position_mean, 'position_std': position_std
    }
    np.save('nn_normalization_params.npy', normalization_params)
    print("归一化参数已保存到 nn_normalization_params.npy")

    model = build_neural_model()

    callbacks = [
        EarlyStopping(monitor='val_loss', patience=20, restore_best_weights=True, verbose=1),
        ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=8, min_lr=1e-7, verbose=1),
        ModelCheckpoint('best_location_model.keras', monitor='val_loss', save_best_only=True)
    ]

    print(f"开始训练模型，共 {epochs} 轮...")
    history = model.fit(
        [X_measurements_norm, X_platform_norm],
        Y_positions_norm,
        validation_split=0.2,
        epochs=epochs,
        batch_size=batch_size,
        callbacks=callbacks,
        verbose=1,
        shuffle=True
    )
    
    print("模型训练完成。最佳模型已保存到 best_location_model.keras")
    return model, normalization_params, history

def neural_network_prediction(model, measurement_obs, Plat_Nav_Data, normalization_params):
    """改进的神经网络预测函数"""
    platform = Plat_Nav_Data[:6, :].T

    measurements_norm = (measurement_obs - normalization_params['measurement_mean']) / (normalization_params['measurement_std'] + 1e-8)
    platform_norm = (platform - normalization_params['platform_mean']) / (normalization_params['platform_std'] + 1e-8)

    predictions_norm = model.predict([measurements_norm, platform_norm], verbose=0)

    predictions = predictions_norm * normalization_params['position_std'] + normalization_params['position_mean']

    # 使用加权平均而不是简单中位数，给质量更好的测量更高权重
    if len(predictions) > 1:
        # 计算每个预测的置信度（基于测量质量）
        weights = []
        for i, meas in enumerate(measurement_obs):
            # 使用SNR估计和相关系数作为权重
            if len(meas) >= 6:  # 确保有SNR特征
                snr_weight = max(0.1, min(1.0, (meas[5] + 10) / 20))  # SNR权重
                corr_weight = max(0.1, min(1.0, meas[2]))  # 相关系数权重
                weights.append(snr_weight * corr_weight)
            else:
                weights.append(1.0)

        weights = np.array(weights)
        weights = weights / np.sum(weights)

        final_pos = np.average(predictions, axis=0, weights=weights)
    else:
        final_pos = predictions[0] if len(predictions) > 0 else np.array([0, 0, 0])

    # 添加频率估计以保持兼容性
    if len(final_pos) == 3:
        freq_est = np.mean([meas[0] for meas in measurement_obs])  # 使用平均频率
        final_pos = np.append(final_pos, freq_est)

    return final_pos

# ==============================================================================
# 4. 主流程、绘图与精度评估 (Main Flow, Plotting, and Accuracy Evaluation)
# ==============================================================================

def plot_3d_scene(Px, Py, Pz, p_true, p_est_nn, snr_db):
    """
    (新增) 绘制三维场景示意图
    """
    error_nn = np.linalg.norm(p_est_nn[:3] - p_true[:3])
    
    fig3d = plt.figure(figsize=(12, 10))
    ax3d = fig3d.add_subplot(111, projection='3d')

    # 绘制平台轨迹
    ax3d.plot(Px, Py, Pz, 'b-', label='平台轨迹')

    # 绘制真实位置和估计位置
    ax3d.scatter(p_true[0], p_true[1], p_true[2], s=150, c='red', marker='x', depthshade=True, label='真实位置')
    ax3d.scatter(p_est_nn[0], p_est_nn[1], p_est_nn[2], s=120, c='green', marker='o', depthshade=True, label='估计位置')

    # 绘制误差连线
    ax3d.plot([p_true[0], p_est_nn[0]], [p_true[1], p_est_nn[1]], [p_true[2], p_est_nn[2]], 'k--', label=f'误差: {error_nn:.2f} m')

    # 绘制从地心到真实位置的辅助线
    ax3d.plot([0, p_true[0]], [0, p_true[1]], [0, p_true[2]], 'r:', alpha=0.5)
    
    # 设置标签和标题
    ax3d.set_xlabel('X 位置 (m)')
    ax3d.set_ylabel('Y 位置 (m)')
    ax3d.set_zlabel('Z 位置 (m)')
    ax3d.set_title(f'三维定位场景示意图 (SNR = {snr_db} dB)')
    ax3d.legend()

    # 调整视角和布局
    ax3d.view_init(elev=25., azim=-75)
    plt.tight_layout()
    plt.savefig('localization_3d_scene.png')
    # plt.show() # 注释掉，避免阻塞主流程，让2D和3D图一起显示

def run_single_localization(p_true, snr_db=12, train_model_flag=False):
    """(已修改) 运行一次完整的从IQ到定位的流程，并生成2D和3D图"""
    g, T, del_T, alt_kft, vel, fo = 2, 10, 0.1, 10, 200, 1e9
    Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
    Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])

    measurement_obs = []
    print("正在从IQ数据中提取AOA/多普勒测量值...")
    for j in range(len(Px)):
        platform_pos = np.array([Px[j], Py[j], Pz[j]])
        platform_vel = np.array([Vx[j], Vy[j], Vz[j]])
        mu_vect_single = mu_vect[:, j]
        iq_data = generate_iq_data(p_true, platform_pos, platform_vel, mu_vect_single, fo, snr_db)
        f_j, features = extract_aoa_doppler_from_iq(iq_data, fo, fs=2e4)
        measurement_obs.append([f_j] + features)
    
    measurement_obs = np.array(measurement_obs)

    if train_model_flag or not os.path.exists('best_location_model.keras'):
        print("需要训练模型...")
        X_m, X_p, Y_p = generate_training_data(num_scenarios=2000)
        model, norm_params, _ = train_neural_model(X_m, X_p, Y_p)
    else:
        print("加载预训练模型...")
        # 定义自定义损失函数用于加载模型
        def position_loss(y_true, y_pred):
            position_error = y_true - y_pred
            huber_delta = 50.0
            abs_error = tf.abs(position_error)
            quadratic = tf.minimum(abs_error, huber_delta)
            linear = abs_error - quadratic
            huber_loss = 0.5 * quadratic**2 + huber_delta * linear
            return tf.reduce_mean(huber_loss)

        model = tf.keras.models.load_model('best_location_model.keras', custom_objects={'position_loss': position_loss})
        norm_params = np.load('nn_normalization_params.npy', allow_pickle=True).item()

    p_est_nn = neural_network_prediction(model, measurement_obs, Plat_Nav_Data, norm_params)
    error_nn = np.linalg.norm(p_est_nn[:3] - p_true[:3])
    
    print(f"\n真实位置: {p_true[:3]}")
    print(f"神经网络估计位置: {p_est_nn[:3]}")
    print(f"定位误差: {error_nn:.2f} m")

    # --- 绘制二维俯视图 ---
    plt.figure(figsize=(10, 8))
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.plot(Px, Py, 'b-', label='平台路径')
    plt.scatter(p_true[0], p_true[1], s=150, c='red', marker='x', label='真实位置')
    plt.scatter(p_est_nn[0], p_est_nn[1], s=120, c='green', marker='o', label=f'神经网络估计 (误差: {error_nn:.2f} m)')
    plt.title(f'二维定位结果俯视图 (SNR = {snr_db} dB)')
    plt.xlabel('X 位置 (m)')
    plt.ylabel('Y 位置 (m)')
    plt.axis('equal')
    plt.legend()
    plt.savefig('localization_2d_result.png')
    
    # --- 绘制三维场景图 ---
    plot_3d_scene(Px, Py, Pz, p_true, p_est_nn, snr_db)
    
    plt.show() # 一起显示所有图片


def run_accuracy_test(num_tests=200, snr_db=12):
    """(保持不变) 运行蒙特卡洛模拟以测试定位准确率。"""
    if not os.path.exists('best_location_model.keras'):
        print("错误：找不到模型文件 'best_location_model.keras'。请先使用 --train 参数进行训练。")
        return

    print(f"开始精度评估测试，共 {num_tests} 次，SNR = {snr_db} dB...")
    print("标准：误差 <= max(100m, 5% of 真实距离) 视为成功。")

    # 定义自定义损失函数用于加载模型
    def position_loss(y_true, y_pred):
        position_error = y_true - y_pred
        huber_delta = 50.0
        abs_error = tf.abs(position_error)
        quadratic = tf.minimum(abs_error, huber_delta)
        linear = abs_error - quadratic
        huber_loss = 0.5 * quadratic**2 + huber_delta * linear
        return tf.reduce_mean(huber_loss)

    model = tf.keras.models.load_model('best_location_model.keras', custom_objects={'position_loss': position_loss})
    norm_params = np.load('nn_normalization_params.npy', allow_pickle=True).item()

    successful_localizations = 0
    errors = []
    
    g, T, del_T, alt_kft, vel, fo = 2, 10, 0.1, 10, 200, 1e9

    for i in tqdm(range(num_tests), desc="精度评估中"):
        area_size = 7000
        x_true = np.random.uniform(-area_size, area_size)
        y_true = np.random.uniform(-area_size, area_size)
        z_true = np.random.uniform(0, 500)
        p_true_full = np.array([x_true, y_true, z_true, fo])  # 用于IQ数据生成
        p_true = np.array([x_true, y_true, z_true])  # 用于误差计算

        Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
        Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])

        measurement_obs = []
        for j in range(len(Px)):
            platform_pos = np.array([Px[j], Py[j], Pz[j]])
            platform_vel = np.array([Vx[j], Vy[j], Vz[j]])
            mu_vect_single = mu_vect[:, j]
            iq_data = generate_iq_data(p_true_full, platform_pos, platform_vel, mu_vect_single, fo, snr_db)
            f_j, features = extract_aoa_doppler_from_iq(iq_data, fo, fs=2e4)
            measurement_obs.append([f_j] + features)
        measurement_obs = np.array(measurement_obs)

        p_est_nn = neural_network_prediction(model, measurement_obs, Plat_Nav_Data, norm_params)
        
        error = np.linalg.norm(p_est_nn[:3] - p_true[:3])
        true_distance = np.linalg.norm(p_true[:3])
        error_threshold = max(100.0, 0.05 * true_distance)
        errors.append(error)

        if error <= error_threshold:
            successful_localizations += 1
    
    accuracy = (successful_localizations / num_tests) * 100
    
    print("\n" + "="*50)
    print(" 精  度  评  估  报  告")
    print("="*50)
    print(f" 测试总数: {num_tests}")
    print(f" 信噪比 (SNR): {snr_db} dB")
    print(f" 成功定位次数: {successful_localizations}")
    print(f" 定位准确率: {accuracy:.2f}%")
    print("-" * 50)
    if accuracy >= 85.0:
        print("✅ 目标达成：定位准确率不低于85%。")
    else:
        print("❌ 目标未达成：定位准确率低于85%。")
    print("="*50)
    
    plt.figure(figsize=(10, 6))
    plt.hist(errors, bins=50, alpha=0.7, label='误差分布')
    plt.axvline(np.mean(errors), color='red', linestyle='--', label=f'平均误差: {np.mean(errors):.2f} m')
    plt.title(f'定位误差分布直方图 (N={num_tests}, SNR={snr_db}dB)')
    plt.xlabel('定位误差 (m)')
    plt.ylabel('次数')
    plt.legend()
    plt.grid(True, alpha=0.5)
    plt.savefig('accuracy_test_error_distribution.png')
    plt.show()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='基于IQ数据的辐射源定位系统')
    parser.add_argument('--train', action='store_true', help='强制重新训练模型。')
    parser.add_argument('--test-accuracy', action='store_true', help='运行精度评估测试。')
    parser.add_argument('--snr', type=float, default=12.0, help='设置测试的信噪比(dB)。')
    parser.add_argument('--num-tests', type=int, default=200, help='设置精度评估的测试次数。')

    args = parser.parse_args()

    if not USE_TENSORFLOW:
        print("错误：此脚本需要TensorFlow。请安装TensorFlow后重试。")
        sys.exit(1)

    if args.train:
        print("开始训练流程...")
        X_m, X_p, Y_p = generate_training_data(num_scenarios=3000, snr_range=(12, 25))
        train_neural_model(X_m, X_p, Y_p, epochs=100, batch_size=256)
    
    elif args.test_accuracy:
        run_accuracy_test(num_tests=args.num_tests, snr_db=args.snr)
        
    else:
        print("运行单次定位演示...")
        p_true_demo = np.array([2500, 1500, 200, 1e9])
        run_single_localization(p_true_demo, snr_db=args.snr, train_model_flag=False)
        print("\n提示：使用 --train 进行模型训练，使用 --test-accuracy 进行精度评估。")
