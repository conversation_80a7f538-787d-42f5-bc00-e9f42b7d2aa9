import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D # 导入3D绘图工具
from scipy.optimize import least_squares
from matplotlib.patches import Ellipse
import sys
import io
import os
import argparse
import time
from tqdm import tqdm

# 解决控制台输出编码问题
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 尝试导入TensorFlow，如果失败则设置标志
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import Dense, Input, Concatenate, BatchNormalization, Dropout, GaussianNoise
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
    USE_TENSORFLOW = True
    print("成功加载TensorFlow。")
except ImportError:
    USE_TENSORFLOW = False
    print("未能加载TensorFlow，将只使用传统方法。")

# ==============================================================================
# 1. 平台运动与几何关系 (Platform Motion and Geometry)
# ==============================================================================

def calculate_measurements(params, Plat_Nav_Data, mu_vect, fo):
    """计算理想的多普勒和AOA测量值"""
    c = 2.998e8
    lambda_ = c / fo
    xe, ye, ze, fo_est = params
    Px, Py, Pz, Vx, Vy, Vz = Plat_Nav_Data[0, :], Plat_Nav_Data[1, :], Plat_Nav_Data[2, :], Plat_Nav_Data[3, :], Plat_Nav_Data[4, :], Plat_Nav_Data[5, :]
    R = np.sqrt((Px - xe)**2 + (Py - ye)**2 + (Pz - ze)**2)
    f = fo_est - (fo_est/c)*(Vx*(Px-xe) + Vy*(Py-ye) + Vz*(Pz-ze))/R
    phi = - (2*np.pi/lambda_)*(mu_vect[0,:]*(Px-xe) + mu_vect[1,:]*(Py-ye) + mu_vect[2,:]*(Pz-ze))/R
    return f, phi

def residuals(params, Plat_Nav_Data, mu_vect, fo, f_obs, phi_obs, freq_accuracy, phi_accuracy):
    """最小二乘优化的残差函数"""
    # 计算预期测量值
    f_exp, phi_exp = calculate_measurements(params, Plat_Nav_Data, mu_vect, fo)

    # 计算加权残差
    res_dop = (f_exp - f_obs) / freq_accuracy
    res_aoa = (phi_exp - phi_obs) / phi_accuracy

    # 合并残差
    res = np.concatenate([res_dop, res_aoa])

    return res

def traditional_localization(measurement_obs, Plat_Nav_Data, mu_vect, fo):
    """传统最小二乘定位方法"""
    # 从IQ特征中提取频率和相位信息
    f_obs = measurement_obs[:, 0]  # 频率测量

    # 从IQ特征中构造相位测量（使用相位差特征）
    phi_obs = measurement_obs[:, 2]  # 使用cos_theta特征反推相位

    # 设置测量精度
    freq_accuracy = np.ones_like(f_obs) * 10.0  # 10Hz频率精度
    phi_accuracy = np.ones_like(phi_obs) * 0.1   # 0.1弧度相位精度

    # 初始估计 - 使用平台轨迹中心作为初始猜测
    center_x = np.mean(Plat_Nav_Data[0, :])
    center_y = np.mean(Plat_Nav_Data[1, :])
    center_z = np.mean(Plat_Nav_Data[2, :])

    # 基于多普勒频移的粗略距离估计
    avg_doppler = np.mean(f_obs - fo)
    rough_distance = abs(avg_doppler) * 2.998e8 / fo * 100  # 粗略估计
    rough_distance = max(1000, min(rough_distance, 20000))  # 限制在合理范围

    p_est_init = np.array([center_x + rough_distance/3, center_y + rough_distance/3, center_z/2, fo])

    try:
        # 运行最小二乘优化
        result = least_squares(
            lambda params: residuals(params, Plat_Nav_Data, mu_vect, fo, f_obs, phi_obs, freq_accuracy, phi_accuracy),
            p_est_init,
            method='trf',
            ftol=1e-8,
            xtol=1e-8,
            gtol=1e-8,
            max_nfev=3000,
            verbose=0
        )

        if result.success:
            return result.x[:3], result.cost
        else:
            return p_est_init[:3], float('inf')

    except Exception as e:
        return p_est_init[:3], float('inf')

# ==============================================================================
# 1. 平台运动与几何关系 (Platform Motion and Geometry)
# ==============================================================================

def weave(g, T, del_T, alt_kft, vel):
    """平台导航数据生成函数"""
    dt = del_T
    dtt = del_T/20
    accelh = g * 9.81
    accelv = 0.5 * 9.81
    alt0 = alt_kft * 1000 * 0.3048

    dtr = np.pi / 180
    maxturn = 30 * dtr
    vhoriz = vel
    radturn = (vhoriz**2) / accelh
    turnrate = vel / radturn
    turndur = 2 * maxturn / turnrate
    turndur = np.floor(turndur/dtt) * dtt
    gamma0 = -np.arcsin(min(accelv*turndur/vel, 0.5))/2

    px, py, pz = 0, 0, alt0
    XYZA, XYZADOT, ah = [], [], []
    itt = 0

    for tt in np.arange(0, T + dtt, dtt):
        trem = tt % (2 * turndur)
        if trem < turndur:
            turnangl = maxturn - turnrate * trem
            ahoriz = accelh
            az = accelv
        else:
            turnangl = -maxturn + turnrate * (trem - turndur)
            ahoriz = -accelh
            az = -accelv

        vx = vhoriz * np.cos(turnangl)
        vy = -vhoriz * np.sin(turnangl)

        if itt > 0:
            vz = XYZADOT[-1][2] + az * dtt if XYZADOT else vel * np.sin(gamma0)
            px += vx * dtt
            py += vy * dtt
            pz += vz * dtt
        else:
            vz = vel * np.sin(gamma0)

        if abs(np.mod(itt * dtt, dt)) < 1e-6:
            XYZA.append([px, py, pz])
            XYZADOT.append([vx, vy, vz])
            ah.append(ahoriz)

        itt += 1

    XYZA = np.array(XYZA).T
    XYZADOT = np.array(XYZADOT).T
    ah = np.array(ah)

    hdg = np.pi/2 - np.arctan2(XYZADOT[1, :], XYZADOT[0, :])
    long_vect = np.vstack([np.sin(hdg), np.cos(hdg), np.zeros_like(hdg)])
    vect_norms = np.sqrt(np.sum(long_vect**2, axis=0))
    long_vect = long_vect / vect_norms

    return XYZA[0, :], XYZA[1, :], XYZA[2, :], XYZADOT[0, :], XYZADOT[1, :], XYZADOT[2, :], long_vect


def calculate_measurements(params, Plat_Nav_Data, mu_vect, fo):
    """(保持不变) 计算理想的多普勒和AOA测量值"""
    c = 2.998e8
    lambda_ = c / fo
    xe, ye, ze, fo_est = params
    Px, Py, Pz, Vx, Vy, Vz = Plat_Nav_Data[0, :], Plat_Nav_Data[1, :], Plat_Nav_Data[2, :], Plat_Nav_Data[3, :], Plat_Nav_Data[4, :], Plat_Nav_Data[5, :]
    R = np.sqrt((Px - xe)**2 + (Py - ye)**2 + (Pz - ze)**2)
    f = fo_est - (fo_est/c)*(Vx*(Px-xe) + Vy*(Py-ye) + Vz*(Pz-ze))/R
    phi = - (2*np.pi/lambda_)*(mu_vect[0,:]*(Px-xe) + mu_vect[1,:]*(Py-ye) + mu_vect[2,:]*(Pz-ze))/R
    return f, phi

# ==============================================================================
# 2. IQ数据生成与信号处理 (IQ Data Generation and Signal Processing)
# ==============================================================================

def generate_iq_data(p_true, platform_pos, platform_vel, mu_vect_single, fo, snr_db, L=2, d=0.5, fs=2e4, num_samples=2048):
    """增强的IQ数据生成函数 - 更真实的信号模型和物理效应"""
    c = 2.998e8
    lambda_ = c / fo
    xe, ye, ze, fo_true = p_true
    Px, Py, Pz = platform_pos
    Vx, Vy, Vz = platform_vel

    R_vec = np.array([Px - xe, Py - ye, Pz - ze])
    R = np.linalg.norm(R_vec)
    los_vec_norm = R_vec / R

    # 改进的多普勒频率计算
    doppler_freq = -(fo / c) * np.dot(platform_vel, los_vec_norm)
    cos_theta = -np.dot(los_vec_norm, mu_vect_single)

    t = np.arange(num_samples) / fs
    signal_power = 1.0
    iq_data = np.zeros((L, num_samples), dtype=np.complex128)

    # 更真实的信号参数
    freq_stability = 1e-8  # 频率稳定度
    phase_noise_corner_freq = 1000  # 相位噪声拐点频率

    # 生成更真实的频率和相位噪声
    freq_drift = np.random.normal(0, freq_stability * fo)

    for i in range(L):
        # 基本相位偏移 - 考虑阵列几何
        phase_shift = (2 * np.pi * d * i / lambda_) * cos_theta

        # 改进的相位噪声模型 - 1/f噪声
        white_noise = np.random.normal(0, 1, num_samples)
        # 简化的1/f噪声近似
        phase_noise = np.cumsum(white_noise) * 0.001

        # 添加振荡器相位噪声
        osc_phase_noise = np.random.normal(0, 0.005, num_samples)
        total_phase_noise = phase_noise + osc_phase_noise

        # 生成基带信号
        instantaneous_freq = doppler_freq + freq_drift
        instantaneous_phase = 2 * np.pi * instantaneous_freq * t + phase_shift + total_phase_noise
        signal = np.exp(1j * instantaneous_phase)

        # 添加更真实的幅度效应
        # 1. 路径损耗起伏
        path_loss_variation = 1.0 + 0.02 * np.random.normal(0, 1)

        # 2. 快衰落效应（简化）
        fading_freq = 10  # Hz
        fading_amplitude = 0.1
        fading = 1.0 + fading_amplitude * np.sin(2 * np.pi * fading_freq * t + np.random.uniform(0, 2*np.pi))

        # 3. 接收机增益变化
        gain_variation = 1.0 + 0.01 * np.random.normal(0, 1, num_samples)

        # 组合所有幅度效应
        amplitude_effects = path_loss_variation * fading * gain_variation
        signal = signal * amplitude_effects

        iq_data[i, :] = signal

    # 改进的噪声模型
    snr_linear = 10**(snr_db / 10.0)
    noise_power = signal_power / snr_linear
    noise_std = np.sqrt(noise_power / 2)

    # 更真实的噪声特性
    if L > 1:
        # 通道间相关噪声（接收机共同噪声源）
        correlation_coeff = 0.15

        # 生成相关噪声
        independent_noise = (np.random.normal(0, noise_std * np.sqrt(1 - correlation_coeff), size=iq_data.shape) +
                           1j * np.random.normal(0, noise_std * np.sqrt(1 - correlation_coeff), size=iq_data.shape))

        common_noise_real = np.random.normal(0, noise_std * np.sqrt(correlation_coeff), size=(1, num_samples))
        common_noise_imag = np.random.normal(0, noise_std * np.sqrt(correlation_coeff), size=(1, num_samples))
        common_noise = common_noise_real + 1j * common_noise_imag
        common_noise = np.repeat(common_noise, L, axis=0)

        noise = independent_noise + common_noise
    else:
        noise = (np.random.normal(0, noise_std, size=iq_data.shape) +
                1j * np.random.normal(0, noise_std, size=iq_data.shape))

    # 添加量化噪声（ADC效应）
    adc_bits = 12  # 12位ADC
    adc_range = 2.0
    quantization_step = adc_range / (2**adc_bits)
    quantization_noise = (np.random.uniform(-0.5, 0.5, size=iq_data.shape) +
                         1j * np.random.uniform(-0.5, 0.5, size=iq_data.shape)) * quantization_step

    return iq_data + noise + quantization_noise

def extract_aoa_doppler_from_iq(iq_data, fo, fs, L=2, d=0.5):
    """增强的IQ数据特征提取方法 - 多种算法融合"""
    num_samples = iq_data.shape[1]
    c = 2.998e8
    lambda_ = c / fo

    # --- 多种多普勒估计方法融合 ---
    # 方法1: FFT峰值检测
    window = np.hanning(num_samples)
    avg_signal = np.mean(iq_data, axis=0)
    windowed_signal = avg_signal * window
    fft_result = np.fft.fft(windowed_signal)
    fft_freqs = np.fft.fftfreq(num_samples, 1/fs)
    power_spectrum = np.abs(fft_result)**2
    peak_index = np.argmax(power_spectrum)
    doppler_fft = fft_freqs[peak_index]

    # 方法2: 自相关方法
    lag_samples = min(100, num_samples // 4)
    autocorr = np.correlate(avg_signal, avg_signal, mode='full')
    center = len(autocorr) // 2
    autocorr_segment = autocorr[center:center + lag_samples]
    if len(autocorr_segment) > 1:
        peak_lag = np.argmax(np.abs(autocorr_segment[1:]))  + 1
        doppler_autocorr = fs / peak_lag if peak_lag > 0 else 0
    else:
        doppler_autocorr = 0

    # 方法3: 相位差分方法
    phase_signal = np.angle(avg_signal)
    phase_unwrapped = np.unwrap(phase_signal)
    if num_samples > 1:
        phase_diff = np.diff(phase_unwrapped)
        doppler_phase = np.mean(phase_diff) * fs / (2 * np.pi)
    else:
        doppler_phase = 0

    # 融合多普勒估计（加权平均）
    doppler_estimates = [doppler_fft, doppler_autocorr, doppler_phase]
    weights = [0.5, 0.3, 0.2]  # FFT方法权重最高
    doppler_est = np.average([d for d in doppler_estimates if abs(d) < fs/2],
                           weights=[w for d, w in zip(doppler_estimates, weights) if abs(d) < fs/2])

    f_est = fo + doppler_est

    # --- 增强的AOA特征提取 ---
    if L >= 2:
        # 1. 基础相关分析
        cross_correlation = np.mean(iq_data[1, :] * np.conj(iq_data[0, :]))
        phase_diff = np.angle(cross_correlation)
        corr_magnitude = np.abs(cross_correlation)

        # 2. 功率归一化
        power_0 = np.mean(np.abs(iq_data[0, :])**2)
        power_1 = np.mean(np.abs(iq_data[1, :])**2)
        normalized_corr = corr_magnitude / np.sqrt(power_0 * power_1 + 1e-12)

        # 3. 改进的AOA估计
        phase_diff_unwrapped = np.unwrap([0, phase_diff])[1]
        cos_theta_raw = phase_diff_unwrapped * lambda_ / (2 * np.pi * d)
        cos_theta = np.clip(cos_theta_raw, -1, 1)

        # 4. 多种AOA估计方法
        # MUSIC算法简化版本
        R = np.cov(iq_data)  # 协方差矩阵
        eigenvals, eigenvecs = np.linalg.eigh(R)
        noise_subspace = eigenvecs[:, :-1]  # 假设只有一个信号源

        # 简化的MUSIC谱
        angles = np.linspace(-np.pi/2, np.pi/2, 180)
        music_spectrum = []
        for angle in angles:
            steering_vector = np.exp(1j * 2 * np.pi * d * np.arange(L) * np.sin(angle) / lambda_)
            denominator = np.abs(steering_vector.conj().T @ noise_subspace @ noise_subspace.conj().T @ steering_vector)
            music_spectrum.append(1 / (denominator + 1e-12))

        music_peak_idx = np.argmax(music_spectrum)
        aoa_music = angles[music_peak_idx]

        # 5. 信号质量特征
        signal_power = np.mean([power_0, power_1])

        # 改进的SNR估计
        signal_samples = np.concatenate([iq_data[0, :], iq_data[1, :]])
        signal_magnitude = np.abs(signal_samples)
        signal_est = np.percentile(signal_magnitude, 90)  # 使用90%分位数作为信号强度
        noise_est = np.percentile(signal_magnitude, 10)   # 使用10%分位数作为噪声强度
        snr_est = 20 * np.log10((signal_est + 1e-12) / (noise_est + 1e-12))

        # 6. 相位稳定性分析
        phase_0 = np.angle(iq_data[0, :])
        phase_1 = np.angle(iq_data[1, :])
        phase_diff_series = np.unwrap(phase_1 - phase_0)
        phase_stability = 1.0 / (1.0 + np.std(phase_diff_series))

        # 7. 频域特征
        fft_0 = np.fft.fft(iq_data[0, :])
        fft_1 = np.fft.fft(iq_data[1, :])
        freq_corr = np.abs(np.mean(fft_1 * np.conj(fft_0))) / np.sqrt(np.mean(np.abs(fft_0)**2) * np.mean(np.abs(fft_1)**2) + 1e-12)

        # 8. 幅度特征
        amp_0 = np.abs(iq_data[0, :])
        amp_1 = np.abs(iq_data[1, :])
        amp_ratio = np.mean(amp_1) / (np.mean(amp_0) + 1e-12)
        amp_correlation = np.corrcoef(amp_0, amp_1)[0, 1]
        if np.isnan(amp_correlation):
            amp_correlation = 0.0

        # 9. 时域特征
        time_delay_est = np.argmax(np.abs(np.correlate(iq_data[1, :], iq_data[0, :], mode='full'))) - num_samples + 1
        time_delay_normalized = time_delay_est / num_samples

        features = [
            normalized_corr,      # 归一化相关系数
            phase_diff,          # 相位差
            cos_theta,           # cos(theta) - 基础AOA特征
            np.sin(aoa_music),   # MUSIC算法AOA特征
            snr_est,             # 改进的SNR估计
            phase_stability,     # 相位稳定性
            freq_corr,           # 频域相关性
            amp_ratio,           # 幅度比
            amp_correlation,     # 幅度相关性
            time_delay_normalized, # 归一化时延
            doppler_est / 1000   # 归一化的多普勒频移
        ]
    else:
        features = [0] * 11  # 增加特征数量

    return f_est, features


# ==============================================================================
# 3. 神经网络模型与训练 (Neural Network Model and Training)
# ==============================================================================

def build_neural_model(input_shape_measurements=12, input_shape_platform=6):
    """增强的神经网络模型 - 深度架构与注意力机制"""
    measurement_input = Input(shape=(input_shape_measurements,), name='measurement_input')
    platform_input = Input(shape=(input_shape_platform,), name='platform_input')

    # === 测量特征分支 - 专门处理IQ特征 ===
    # 第一层：特征提取
    meas_branch = Dense(128, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-5))(measurement_input)
    meas_branch = BatchNormalization()(meas_branch)
    meas_branch = Dropout(0.2)(meas_branch)

    # 第二层：特征增强
    meas_branch = Dense(96, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-5))(meas_branch)
    meas_branch = BatchNormalization()(meas_branch)
    meas_branch = Dropout(0.15)(meas_branch)

    # 第三层：特征压缩
    meas_branch = Dense(64, activation='relu')(meas_branch)
    meas_branch = BatchNormalization()(meas_branch)

    # === 平台特征分支 - 专门处理平台数据 ===
    # 第一层：位置和速度特征
    plat_branch = Dense(64, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-5))(platform_input)
    plat_branch = BatchNormalization()(plat_branch)
    plat_branch = Dropout(0.2)(plat_branch)

    # 第二层：运动学特征
    plat_branch = Dense(32, activation='relu')(plat_branch)
    plat_branch = BatchNormalization()(plat_branch)
    plat_branch = Dropout(0.1)(plat_branch)

    # 第三层：几何特征
    plat_branch = Dense(24, activation='relu')(plat_branch)
    plat_branch = BatchNormalization()(plat_branch)

    # === 注意力机制 ===
    # 简化的自注意力机制
    combined_features = Concatenate()([meas_branch, plat_branch])

    # 注意力权重计算
    attention_weights = Dense(combined_features.shape[-1], activation='softmax', name='attention_weights')(combined_features)
    attended_features = tf.keras.layers.Multiply()([combined_features, attention_weights])

    # 添加残差连接
    x = tf.keras.layers.Add()([combined_features, attended_features])
    x = GaussianNoise(0.003)(x)

    # === 深层融合网络 ===
    # 第一层：深度特征融合
    x = Dense(256, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-5))(x)
    x = BatchNormalization()(x)
    x = Dropout(0.3)(x)

    # 第二层：非线性映射
    x = Dense(192, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(1e-5))(x)
    x = BatchNormalization()(x)
    x = Dropout(0.25)(x)

    # 第三层：特征精炼
    x = Dense(128, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dropout(0.2)(x)

    # 第四层：位置预测准备
    x = Dense(64, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dropout(0.15)(x)

    # 第五层：最终特征
    x = Dense(32, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dropout(0.1)(x)

    # === 输出层 ===
    # 位置输出 - 使用线性激活以允许任意范围的坐标
    output = Dense(3, activation='linear', name='position_output')(x)

    model = Model(inputs=[measurement_input, platform_input], outputs=output)

    # === 改进的损失函数 ===
    def enhanced_position_loss(y_true, y_pred):
        # 基础位置误差
        position_error = y_true - y_pred

        # 1. Huber损失 - 对异常值鲁棒
        huber_delta = 100.0  # 100米阈值
        abs_error = tf.abs(position_error)
        quadratic = tf.minimum(abs_error, huber_delta)
        linear = abs_error - quadratic
        huber_loss = 0.5 * quadratic**2 + huber_delta * linear

        # 2. 相对误差损失 - 考虑距离的相对性
        distance_true = tf.sqrt(tf.reduce_sum(y_true**2, axis=1, keepdims=True))
        relative_error = tf.abs(position_error) / (distance_true + 100.0)  # 避免除零
        relative_loss = tf.reduce_mean(relative_error)

        # 3. 组合损失
        total_loss = tf.reduce_mean(huber_loss) + 0.1 * relative_loss

        return total_loss

    # === 优化器配置 ===
    optimizer = Adam(
        learning_rate=0.001,
        beta_1=0.9,
        beta_2=0.999,
        epsilon=1e-7,
        clipnorm=1.0
    )

    model.compile(
        optimizer=optimizer,
        loss=enhanced_position_loss,
        metrics=['mae', 'mse']
    )

    return model


def generate_training_data(num_scenarios=1000, snr_range=(12, 25), area_size=5000):
    """增强的训练数据生成 - 更多样化的场景和数据增强"""
    print(f"正在生成 {num_scenarios} 个场景的训练数据...")
    X_measurements, X_platform, Y_positions = [], [], []
    del_T = 0.1
    fo = 1e9

    # 数据增强参数
    augmentation_factor = 2  # 每个场景生成多个增强样本

    for scenario_idx in tqdm(range(num_scenarios), desc="生成训练数据"):
        # 更多样化的平台参数
        g = np.random.uniform(1.0, 4.0)  # 扩大g值范围
        T = np.random.uniform(6, 20)     # 扩大时间范围
        alt_kft = np.random.uniform(5, 15)  # 扩大高度范围
        vel = np.random.uniform(150, 300)   # 扩大速度范围

        # 更大的目标区域
        x_true = np.random.uniform(-area_size*1.5, area_size*1.5)
        y_true = np.random.uniform(-area_size*1.5, area_size*1.5)
        z_true = np.random.uniform(0, 1000)  # 扩大高度范围
        fo_true = fo * np.random.uniform(0.998, 1.002)  # 稍微扩大频率范围
        p_true = np.array([x_true, y_true, z_true, fo_true])

        # 动态SNR范围
        base_snr = np.random.uniform(*snr_range)

        Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)

        # 为每个场景生成多个增强样本
        for aug_idx in range(augmentation_factor):
            # 随机选择平台位置子集（数据增强）
            num_positions = len(Px)
            if num_positions > 20:  # 如果位置太多，随机选择子集
                selected_indices = np.random.choice(num_positions,
                                                  size=min(20, num_positions),
                                                  replace=False)
            else:
                selected_indices = range(num_positions)

            for j in selected_indices:
                platform_pos = np.array([Px[j], Py[j], Pz[j]])
                platform_vel = np.array([Vx[j], Vy[j], Vz[j]])
                mu_vect_single = mu_vect[:, j]

                # 动态SNR变化（模拟真实环境）
                snr_variation = np.random.normal(0, 2)  # ±2dB变化
                current_snr = np.clip(base_snr + snr_variation, 10, 30)

                # 生成IQ数据
                iq_data = generate_iq_data(p_true, platform_pos, platform_vel,
                                         mu_vect_single, fo, current_snr)
                f_noisy, features = extract_aoa_doppler_from_iq(iq_data, fo, fs=2e4)

                # 数据增强：添加小的随机扰动
                if aug_idx > 0:
                    # 对特征添加小的噪声
                    feature_noise = np.random.normal(0, 0.01, len(features))
                    features = [f + n for f, n in zip(features, feature_noise)]

                    # 对频率添加小的噪声
                    freq_noise = np.random.normal(0, 10)  # ±10Hz
                    f_noisy += freq_noise

                # 组合频率和其他特征
                measurement = np.array([f_noisy] + features)
                platform = np.array([Px[j], Py[j], Pz[j], Vx[j], Vy[j], Vz[j]])

                # 数据质量检查
                if not np.any(np.isnan(measurement)) and not np.any(np.isinf(measurement)):
                    X_measurements.append(measurement)
                    X_platform.append(platform)
                    Y_positions.append(p_true[:3])  # 只使用位置信息

    print(f"数据生成完成。总样本数: {len(X_measurements)}")

    # 转换为numpy数组并检查形状
    X_measurements = np.array(X_measurements)
    X_platform = np.array(X_platform)
    Y_positions = np.array(Y_positions)

    print(f"测量特征形状: {X_measurements.shape}")
    print(f"平台特征形状: {X_platform.shape}")
    print(f"目标位置形状: {Y_positions.shape}")

    return X_measurements, X_platform, Y_positions


def train_neural_model(X_measurements, X_platform, Y_positions, epochs=100, batch_size=64):
    """增强的神经网络训练 - 改进的数据预处理和训练策略"""

    print("开始数据预处理...")

    # === 改进的数据归一化 ===
    # 测量数据归一化 - 使用鲁棒统计量
    measurement_median = np.median(X_measurements, axis=0)
    measurement_mad = np.median(np.abs(X_measurements - measurement_median), axis=0)  # 中位数绝对偏差
    measurement_std = measurement_mad * 1.4826  # 转换为标准差等价物
    measurement_std = np.where(measurement_std < 1e-8, 1.0, measurement_std)  # 避免除零

    X_measurements_norm = (X_measurements - measurement_median) / measurement_std

    # 平台数据归一化
    platform_mean = X_platform.mean(axis=0)
    platform_std = X_platform.std(axis=0)
    platform_std = np.where(platform_std < 1e-8, 1.0, platform_std)
    X_platform_norm = (X_platform - platform_mean) / platform_std

    # 位置数据归一化 - 使用更稳定的方法
    position_mean = Y_positions.mean(axis=0)
    position_std = Y_positions.std(axis=0)
    position_std = np.where(position_std < 1e-8, 1000.0, position_std)  # 默认1km标准差
    Y_positions_norm = (Y_positions - position_mean) / position_std

    # 保存归一化参数
    normalization_params = {
        'measurement_median': measurement_median, 'measurement_std': measurement_std,
        'platform_mean': platform_mean, 'platform_std': platform_std,
        'position_mean': position_mean, 'position_std': position_std
    }
    np.save('nn_normalization_params.npy', normalization_params)
    print("归一化参数已保存到 nn_normalization_params.npy")

    # === 数据质量检查 ===
    print("进行数据质量检查...")
    valid_indices = []
    for i in range(len(X_measurements_norm)):
        if (not np.any(np.isnan(X_measurements_norm[i])) and
            not np.any(np.isinf(X_measurements_norm[i])) and
            not np.any(np.isnan(X_platform_norm[i])) and
            not np.any(np.isnan(Y_positions_norm[i]))):
            valid_indices.append(i)

    print(f"有效样本数: {len(valid_indices)} / {len(X_measurements_norm)}")

    X_measurements_norm = X_measurements_norm[valid_indices]
    X_platform_norm = X_platform_norm[valid_indices]
    Y_positions_norm = Y_positions_norm[valid_indices]

    # === 构建模型 ===
    input_shape_measurements = X_measurements_norm.shape[1]
    input_shape_platform = X_platform_norm.shape[1]
    print(f"构建模型 - 测量特征维度: {input_shape_measurements}, 平台特征维度: {input_shape_platform}")

    model = build_neural_model(input_shape_measurements, input_shape_platform)

    # 打印模型结构
    model.summary()

    # === 改进的回调函数 ===
    callbacks = [
        EarlyStopping(
            monitor='val_loss',
            patience=25,
            restore_best_weights=True,
            verbose=1,
            min_delta=1e-6
        ),
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.7,
            patience=10,
            min_lr=1e-8,
            verbose=1,
            cooldown=5
        ),
        ModelCheckpoint(
            'best_location_model.keras',
            monitor='val_loss',
            save_best_only=True,
            verbose=1
        ),
        tf.keras.callbacks.TerminateOnNaN()
    ]

    # === 训练配置 ===
    print(f"开始训练模型，共 {epochs} 轮...")
    print(f"批次大小: {batch_size}")
    print(f"训练样本数: {len(X_measurements_norm)}")

    # 使用更大的验证集比例以获得更稳定的验证
    validation_split = 0.25

    history = model.fit(
        [X_measurements_norm, X_platform_norm],
        Y_positions_norm,
        validation_split=validation_split,
        epochs=epochs,
        batch_size=batch_size,
        callbacks=callbacks,
        verbose=1,
        shuffle=True
    )

    print("模型训练完成。最佳模型已保存到 best_location_model.keras")

    # === 训练结果分析 ===
    final_train_loss = history.history['loss'][-1]
    final_val_loss = history.history['val_loss'][-1]
    print(f"最终训练损失: {final_train_loss:.6f}")
    print(f"最终验证损失: {final_val_loss:.6f}")

    return model, normalization_params, history

def hybrid_prediction(model, measurement_obs, Plat_Nav_Data, mu_vect, fo, normalization_params):
    """混合预测函数 - 结合传统方法和神经网络"""

    # === 1. 传统方法预测 ===
    traditional_pos, traditional_cost = traditional_localization(measurement_obs, Plat_Nav_Data, mu_vect, fo)

    # === 2. 神经网络预测 ===
    platform = Plat_Nav_Data[:6, :].T

    # 数据归一化
    if 'measurement_median' in normalization_params:
        measurements_norm = (measurement_obs - normalization_params['measurement_median']) / normalization_params['measurement_std']
    else:
        measurements_norm = (measurement_obs - normalization_params['measurement_mean']) / (normalization_params['measurement_std'] + 1e-8)

    platform_norm = (platform - normalization_params['platform_mean']) / (normalization_params['platform_std'] + 1e-8)

    # 数据质量检查
    valid_indices = []
    for i in range(len(measurements_norm)):
        if (not np.any(np.isnan(measurements_norm[i])) and
            not np.any(np.isinf(measurements_norm[i])) and
            not np.any(np.isnan(platform_norm[i]))):
            valid_indices.append(i)

    if len(valid_indices) == 0:
        print("警告：没有有效的测量数据，使用传统方法结果")
        return np.append(traditional_pos, 1e9)

    # 神经网络预测
    measurements_norm_valid = measurements_norm[valid_indices]
    platform_norm_valid = platform_norm[valid_indices]

    try:
        predictions_norm = model.predict([measurements_norm_valid, platform_norm_valid], verbose=0)
        predictions = predictions_norm * normalization_params['position_std'] + normalization_params['position_mean']

        # 融合多个神经网络预测
        if len(predictions) > 1:
            nn_pos = np.median(predictions, axis=0)
        else:
            nn_pos = predictions[0] if len(predictions) > 0 else traditional_pos

    except Exception as e:
        print(f"神经网络预测失败: {e}")
        nn_pos = traditional_pos

    # === 3. 智能融合策略 ===
    # 计算传统方法的置信度（基于优化成本）
    traditional_confidence = 1.0 / (1.0 + traditional_cost)  # 成本越低，置信度越高
    traditional_confidence = max(0.1, min(0.9, traditional_confidence))

    # 计算神经网络的置信度（基于测量质量）
    measurement_obs_valid = measurement_obs[valid_indices] if len(valid_indices) > 0 else measurement_obs

    # 基于信号质量的置信度
    snr_scores = []
    for meas in measurement_obs_valid:
        if len(meas) >= 5:
            snr_est = meas[4] if len(meas) > 4 else 15
            snr_score = max(0.1, min(1.0, (snr_est + 5) / 25))
            snr_scores.append(snr_score)
        else:
            snr_scores.append(0.5)

    nn_confidence = np.mean(snr_scores) if snr_scores else 0.5

    # 基于预测一致性的置信度调整
    if len(predictions) > 1:
        prediction_std = np.std(predictions, axis=0)
        consistency_score = 1.0 / (1.0 + np.mean(prediction_std) / 1000)  # 标准差越小，一致性越好
        nn_confidence *= consistency_score

    # === 4. 自适应权重融合 ===
    # 归一化置信度
    total_confidence = traditional_confidence + nn_confidence
    if total_confidence > 0:
        w_traditional = traditional_confidence / total_confidence
        w_nn = nn_confidence / total_confidence
    else:
        w_traditional = 0.5
        w_nn = 0.5

    # 加权融合
    final_pos = w_traditional * traditional_pos + w_nn * nn_pos

    # === 5. 后处理和约束 ===
    # 物理约束检查
    final_pos[0] = np.clip(final_pos[0], -50000, 50000)
    final_pos[1] = np.clip(final_pos[1], -50000, 50000)
    final_pos[2] = np.clip(final_pos[2], 0, 20000)

    # 合理性检查 - 如果融合结果明显不合理，选择更好的单一方法
    traditional_error_est = np.linalg.norm(traditional_pos - np.mean(Plat_Nav_Data[:3, :], axis=1))
    nn_error_est = np.linalg.norm(nn_pos - np.mean(Plat_Nav_Data[:3, :], axis=1))
    fusion_error_est = np.linalg.norm(final_pos - np.mean(Plat_Nav_Data[:3, :], axis=1))

    # 如果融合结果比两个单独方法都差很多，选择较好的单一方法
    if fusion_error_est > 2 * min(traditional_error_est, nn_error_est):
        if traditional_error_est < nn_error_est:
            final_pos = traditional_pos
            print("使用传统方法结果（融合结果不合理）")
        else:
            final_pos = nn_pos
            print("使用神经网络结果（融合结果不合理）")

    # 添加频率估计
    if len(measurement_obs_valid) > 0:
        freq_est = np.mean([meas[0] for meas in measurement_obs_valid])
    else:
        freq_est = 1e9

    # 输出融合信息（调试用）
    print(f"传统方法置信度: {traditional_confidence:.3f}, 神经网络置信度: {nn_confidence:.3f}")
    print(f"融合权重 - 传统: {w_traditional:.3f}, 神经网络: {w_nn:.3f}")

    return np.append(final_pos, freq_est)

# ==============================================================================
# 4. 主流程、绘图与精度评估 (Main Flow, Plotting, and Accuracy Evaluation)
# ==============================================================================

def plot_3d_scene(Px, Py, Pz, p_true, p_est_nn, snr_db):
    """
    (新增) 绘制三维场景示意图
    """
    error_nn = np.linalg.norm(p_est_nn[:3] - p_true[:3])
    
    fig3d = plt.figure(figsize=(12, 10))
    ax3d = fig3d.add_subplot(111, projection='3d')

    # 绘制平台轨迹
    ax3d.plot(Px, Py, Pz, 'b-', label='平台轨迹')

    # 绘制真实位置和估计位置
    ax3d.scatter(p_true[0], p_true[1], p_true[2], s=150, c='red', marker='x', depthshade=True, label='真实位置')
    ax3d.scatter(p_est_nn[0], p_est_nn[1], p_est_nn[2], s=120, c='green', marker='o', depthshade=True, label='估计位置')

    # 绘制误差连线
    ax3d.plot([p_true[0], p_est_nn[0]], [p_true[1], p_est_nn[1]], [p_true[2], p_est_nn[2]], 'k--', label=f'误差: {error_nn:.2f} m')

    # 绘制从地心到真实位置的辅助线
    ax3d.plot([0, p_true[0]], [0, p_true[1]], [0, p_true[2]], 'r:', alpha=0.5)
    
    # 设置标签和标题
    ax3d.set_xlabel('X 位置 (m)')
    ax3d.set_ylabel('Y 位置 (m)')
    ax3d.set_zlabel('Z 位置 (m)')
    ax3d.set_title(f'三维定位场景示意图 (SNR = {snr_db} dB)')
    ax3d.legend()

    # 调整视角和布局
    ax3d.view_init(elev=25., azim=-75)
    plt.tight_layout()
    plt.savefig('localization_3d_scene.png')
    # plt.show() # 注释掉，避免阻塞主流程，让2D和3D图一起显示

def run_single_localization(p_true, snr_db=12, train_model_flag=False):
    """(已修改) 运行一次完整的从IQ到定位的流程，并生成2D和3D图"""
    g, T, del_T, alt_kft, vel, fo = 2, 10, 0.1, 10, 200, 1e9
    Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
    Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])

    measurement_obs = []
    print("正在从IQ数据中提取AOA/多普勒测量值...")
    for j in range(len(Px)):
        platform_pos = np.array([Px[j], Py[j], Pz[j]])
        platform_vel = np.array([Vx[j], Vy[j], Vz[j]])
        mu_vect_single = mu_vect[:, j]
        iq_data = generate_iq_data(p_true, platform_pos, platform_vel, mu_vect_single, fo, snr_db)
        f_j, features = extract_aoa_doppler_from_iq(iq_data, fo, fs=2e4)
        measurement_obs.append([f_j] + features)
    
    measurement_obs = np.array(measurement_obs)

    if train_model_flag or not os.path.exists('best_location_model.keras'):
        print("需要训练模型...")
        X_m, X_p, Y_p = generate_training_data(num_scenarios=2000)
        model, norm_params, _ = train_neural_model(X_m, X_p, Y_p)
    else:
        print("加载预训练模型...")
        # 定义自定义损失函数用于加载模型
        def enhanced_position_loss(y_true, y_pred):
            # 基础位置误差
            position_error = y_true - y_pred

            # 1. Huber损失 - 对异常值鲁棒
            huber_delta = 100.0  # 100米阈值
            abs_error = tf.abs(position_error)
            quadratic = tf.minimum(abs_error, huber_delta)
            linear = abs_error - quadratic
            huber_loss = 0.5 * quadratic**2 + huber_delta * linear

            # 2. 相对误差损失 - 考虑距离的相对性
            distance_true = tf.sqrt(tf.reduce_sum(y_true**2, axis=1, keepdims=True))
            relative_error = tf.abs(position_error) / (distance_true + 100.0)  # 避免除零
            relative_loss = tf.reduce_mean(relative_error)

            # 3. 组合损失
            total_loss = tf.reduce_mean(huber_loss) + 0.1 * relative_loss

            return total_loss

        model = tf.keras.models.load_model('best_location_model.keras', custom_objects={'enhanced_position_loss': enhanced_position_loss})
        norm_params = np.load('nn_normalization_params.npy', allow_pickle=True).item()

    p_est_nn = neural_network_prediction(model, measurement_obs, Plat_Nav_Data, norm_params)
    error_nn = np.linalg.norm(p_est_nn[:3] - p_true[:3])
    
    print(f"\n真实位置: {p_true[:3]}")
    print(f"神经网络估计位置: {p_est_nn[:3]}")
    print(f"定位误差: {error_nn:.2f} m")

    # --- 绘制二维俯视图 ---
    plt.figure(figsize=(10, 8))
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.plot(Px, Py, 'b-', label='平台路径')
    plt.scatter(p_true[0], p_true[1], s=150, c='red', marker='x', label='真实位置')
    plt.scatter(p_est_nn[0], p_est_nn[1], s=120, c='green', marker='o', label=f'神经网络估计 (误差: {error_nn:.2f} m)')
    plt.title(f'二维定位结果俯视图 (SNR = {snr_db} dB)')
    plt.xlabel('X 位置 (m)')
    plt.ylabel('Y 位置 (m)')
    plt.axis('equal')
    plt.legend()
    plt.savefig('localization_2d_result.png')
    
    # --- 绘制三维场景图 ---
    plot_3d_scene(Px, Py, Pz, p_true, p_est_nn, snr_db)
    
    plt.show() # 一起显示所有图片


def run_accuracy_test(num_tests=200, snr_db=12):
    """(保持不变) 运行蒙特卡洛模拟以测试定位准确率。"""
    if not os.path.exists('best_location_model.keras'):
        print("错误：找不到模型文件 'best_location_model.keras'。请先使用 --train 参数进行训练。")
        return

    print(f"开始精度评估测试，共 {num_tests} 次，SNR = {snr_db} dB...")
    print("标准：误差 <= max(100m, 5% of 真实距离) 视为成功。")

    # 定义自定义损失函数用于加载模型
    def enhanced_position_loss(y_true, y_pred):
        # 基础位置误差
        position_error = y_true - y_pred

        # 1. Huber损失 - 对异常值鲁棒
        huber_delta = 100.0  # 100米阈值
        abs_error = tf.abs(position_error)
        quadratic = tf.minimum(abs_error, huber_delta)
        linear = abs_error - quadratic
        huber_loss = 0.5 * quadratic**2 + huber_delta * linear

        # 2. 相对误差损失 - 考虑距离的相对性
        distance_true = tf.sqrt(tf.reduce_sum(y_true**2, axis=1, keepdims=True))
        relative_error = tf.abs(position_error) / (distance_true + 100.0)  # 避免除零
        relative_loss = tf.reduce_mean(relative_error)

        # 3. 组合损失
        total_loss = tf.reduce_mean(huber_loss) + 0.1 * relative_loss

        return total_loss

    model = tf.keras.models.load_model('best_location_model.keras', custom_objects={'enhanced_position_loss': enhanced_position_loss})
    norm_params = np.load('nn_normalization_params.npy', allow_pickle=True).item()

    successful_localizations = 0
    errors = []
    
    g, T, del_T, alt_kft, vel, fo = 2, 10, 0.1, 10, 200, 1e9

    for i in tqdm(range(num_tests), desc="精度评估中"):
        area_size = 7000
        x_true = np.random.uniform(-area_size, area_size)
        y_true = np.random.uniform(-area_size, area_size)
        z_true = np.random.uniform(0, 500)
        p_true_full = np.array([x_true, y_true, z_true, fo])  # 用于IQ数据生成
        p_true = np.array([x_true, y_true, z_true])  # 用于误差计算

        Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
        Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])

        measurement_obs = []
        for j in range(len(Px)):
            platform_pos = np.array([Px[j], Py[j], Pz[j]])
            platform_vel = np.array([Vx[j], Vy[j], Vz[j]])
            mu_vect_single = mu_vect[:, j]
            iq_data = generate_iq_data(p_true_full, platform_pos, platform_vel, mu_vect_single, fo, snr_db)
            f_j, features = extract_aoa_doppler_from_iq(iq_data, fo, fs=2e4)
            measurement_obs.append([f_j] + features)
        measurement_obs = np.array(measurement_obs)

        p_est_nn = neural_network_prediction(model, measurement_obs, Plat_Nav_Data, norm_params)
        
        error = np.linalg.norm(p_est_nn[:3] - p_true[:3])
        true_distance = np.linalg.norm(p_true[:3])
        error_threshold = max(100.0, 0.05 * true_distance)
        errors.append(error)

        if error <= error_threshold:
            successful_localizations += 1
    
    accuracy = (successful_localizations / num_tests) * 100
    
    print("\n" + "="*50)
    print(" 精  度  评  估  报  告")
    print("="*50)
    print(f" 测试总数: {num_tests}")
    print(f" 信噪比 (SNR): {snr_db} dB")
    print(f" 成功定位次数: {successful_localizations}")
    print(f" 定位准确率: {accuracy:.2f}%")
    print("-" * 50)
    if accuracy >= 85.0:
        print("✅ 目标达成：定位准确率不低于85%。")
    else:
        print("❌ 目标未达成：定位准确率低于85%。")
    print("="*50)
    
    plt.figure(figsize=(10, 6))
    plt.hist(errors, bins=50, alpha=0.7, label='误差分布')
    plt.axvline(np.mean(errors), color='red', linestyle='--', label=f'平均误差: {np.mean(errors):.2f} m')
    plt.title(f'定位误差分布直方图 (N={num_tests}, SNR={snr_db}dB)')
    plt.xlabel('定位误差 (m)')
    plt.ylabel('次数')
    plt.legend()
    plt.grid(True, alpha=0.5)
    plt.savefig('accuracy_test_error_distribution.png')
    plt.show()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='基于IQ数据的辐射源定位系统')
    parser.add_argument('--train', action='store_true', help='强制重新训练模型。')
    parser.add_argument('--test-accuracy', action='store_true', help='运行精度评估测试。')
    parser.add_argument('--snr', type=float, default=12.0, help='设置测试的信噪比(dB)。')
    parser.add_argument('--num-tests', type=int, default=200, help='设置精度评估的测试次数。')

    args = parser.parse_args()

    if not USE_TENSORFLOW:
        print("错误：此脚本需要TensorFlow。请安装TensorFlow后重试。")
        sys.exit(1)

    if args.train:
        print("开始训练流程...")
        # 先用较少数据进行快速测试
        X_m, X_p, Y_p = generate_training_data(num_scenarios=500, snr_range=(12, 25))
        train_neural_model(X_m, X_p, Y_p, epochs=30, batch_size=64)
    
    elif args.test_accuracy:
        run_accuracy_test(num_tests=args.num_tests, snr_db=args.snr)
        
    else:
        print("运行单次定位演示...")
        p_true_demo = np.array([2500, 1500, 200, 1e9])
        run_single_localization(p_true_demo, snr_db=args.snr, train_model_flag=False)
        print("\n提示：使用 --train 进行模型训练，使用 --test-accuracy 进行精度评估。")
